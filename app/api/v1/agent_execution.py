"""
AI Agent Execution API endpoints.
Handles agent execution, job management, and capabilities.
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Dict, Any, List
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AgentJob, JobStatus, AgentStatus
from app.schemas.agent import (
    AgentExecuteRequest,
    AgentJobResponse,
    AgentCapabilitiesResponse,
    AgentListItem,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service

router = APIRouter()


async def execute_agent_job(
    job_id: uuid.UUID,
    agent: AIAgent,
    input_data: Dict[str, Any],
    db: Session
):
    """
    Background task to execute an agent job
    """
    job = db.query(AgentJob).filter(AgentJob.id == job_id).first()
    if not job:
        return
    
    try:
        # Update job status to running
        job.status = JobStatus.RUNNING
        job.started_at = datetime.now(timezone.utc)
        job.progress_percentage = 10
        db.commit()
        
        # Prepare the execution context
        execution_context = {
            "agent_id": str(agent.id),
            "agent_name": agent.name,
            "model": agent.model_name,
            "temperature": agent.temperature / 100.0,  # Convert to 0-1 range
            "max_tokens": agent.max_tokens,
            "system_prompt": agent.system_prompt,
            **input_data
        }
        
        job.progress_percentage = 30
        db.commit()
        
        # Execute based on job type
        if job.job_type == "execute" or job.job_type == "generate":
            # Use the agent's system prompt and configuration
            start_time = datetime.now()
            
            # Call the anthropic service with agent configuration
            result = await anthropic_service.generate_with_agent_config(
                prompt=execution_context.get("prompt", ""),
                model=agent.model_name,
                temperature=agent.temperature / 100.0,
                max_tokens=agent.max_tokens,
                system_prompt=agent.system_prompt,
                context=execution_context
            )
            
            end_time = datetime.now()
            execution_time = int((end_time - start_time).total_seconds() * 1000)
            
            job.progress_percentage = 90
            db.commit()
            
            # Update job with results
            job.output_data = {
                "result": result,
                "execution_context": execution_context
            }
            job.execution_time_ms = execution_time
            job.model_used = agent.model_name
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.now(timezone.utc)
            job.progress_percentage = 100
            
            # Update agent usage count
            agent.usage_count += 1
            
        else:
            raise ValueError(f"Unsupported job type: {job.job_type}")
        
        db.commit()
        
    except Exception as e:
        # Update job with error
        job.status = JobStatus.FAILED
        job.error_message = str(e)
        job.completed_at = datetime.now(timezone.utc)
        db.commit()


@router.post("/{doc_type}/{agent_id}/execute", response_model=AgentJobResponse)
async def execute_agent(
    doc_type: str,
    agent_id: uuid.UUID,
    request: AgentExecuteRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Execute a specific agent
    """
    # Get agent
    agent = db.query(AIAgent).filter(
        and_(
            AIAgent.id == agent_id,
            AIAgent.doc_type == doc_type,
            AIAgent.status == AgentStatus.ACTIVE,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    ).first()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found or not accessible"
        )
    
    # Check if agent supports the requested operation
    if not agent.capabilities.get("document_generation", False):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Agent does not support document generation"
        )
    
    # Create job
    job = AgentJob(
        agent_id=agent_id,
        job_type=request.job_type,
        input_data=request.input_data,
        document_id=request.document_id,
        user_id=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(job)
    db.commit()
    db.refresh(job)
    
    # Start background execution
    background_tasks.add_task(
        execute_agent_job,
        job.id,
        agent,
        request.input_data,
        db
    )
    
    return AgentJobResponse.from_orm(job)


@router.get("/jobs/{job_id}", response_model=AgentJobResponse)
async def get_agent_job_status(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get agent job status
    """
    job = db.query(AgentJob).filter(
        and_(
            AgentJob.id == job_id,
            AgentJob.user_id == current_user.id,
            AgentJob.tenant_id == current_tenant.id
        )
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    return AgentJobResponse.from_orm(job)


@router.post("/jobs/{job_id}/cancel", response_model=MessageResponse)
async def cancel_agent_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Cancel an agent job
    """
    job = db.query(AgentJob).filter(
        and_(
            AgentJob.id == job_id,
            AgentJob.user_id == current_user.id,
            AgentJob.tenant_id == current_tenant.id
        )
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Job not found"
        )
    
    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Job cannot be cancelled in its current state"
        )
    
    # Update job status
    job.status = JobStatus.CANCELLED
    job.completed_at = datetime.now(timezone.utc)
    job.error_message = "Job cancelled by user"
    
    db.commit()
    
    return MessageResponse(message="Job cancelled successfully")


@router.get("/capabilities/{doc_type}", response_model=AgentCapabilitiesResponse)
async def get_agent_capabilities(
    doc_type: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get agent capabilities for a document type
    """
    # Get all active agents for the document type
    agents = db.query(AIAgent).filter(
        and_(
            AIAgent.doc_type == doc_type,
            AIAgent.status == AgentStatus.ACTIVE,
            or_(
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    ).all()
    
    if not agents:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No agents found for this document type"
        )
    
    # Aggregate capabilities
    all_capabilities = {}
    supported_operations = set()
    
    for agent in agents:
        # Merge capabilities
        for capability, enabled in agent.capabilities.items():
            if enabled:
                all_capabilities[capability] = True
                
                # Map capabilities to operations
                if capability == "document_generation":
                    supported_operations.add("generate")
                    supported_operations.add("execute")
                elif capability == "refinement":
                    supported_operations.add("refine")
                elif capability == "follow_up_questions":
                    supported_operations.add("follow_up")
                elif capability == "suggestions":
                    supported_operations.add("suggest")
    
    return AgentCapabilitiesResponse(
        doc_type=doc_type,
        available_agents=[AgentListItem.from_orm(agent) for agent in agents],
        capabilities=all_capabilities,
        supported_operations=list(supported_operations)
    )
