"""
Form question management API endpoints.
Handles CRUD operations for form questions and conditional logic.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from typing import List, Optional
import uuid
from datetime import datetime, timezone

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.form_schema import FormSchema, FormQuestion, ConditionalRule
from app.schemas.form_schema import (
    QuestionCreate,
    QuestionUpdate,
    QuestionResponse,
    QuestionListResponse,
    ConditionalLogicResponse,
    ConditionalRuleResponse,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for question management"""
    # In a real implementation, check user roles/permissions
    return True


def get_form_schema_or_404(doc_type: str, tenant_id: uuid.UUID, db: Session) -> FormSchema:
    """Get form schema or raise 404"""
    schema = db.query(FormSchema).filter(
        FormSchema.document_type == doc_type,
        FormSchema.tenant_id == tenant_id,
        FormSchema.is_active == True
    ).first()
    
    if not schema:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form schema not found for document type: {doc_type}"
        )
    
    return schema


@router.get("/{doc_type}/questions", response_model=QuestionListResponse)
async def get_questions(
    doc_type: str,
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    section: Optional[str] = Query(None, description="Filter by section"),
    search: Optional[str] = Query(None, description="Search in question text"),
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get questions for document type
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Build query
    query = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True
    )
    
    # Apply filters
    if section:
        query = query.filter(FormQuestion.section == section)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(FormQuestion.question_text.ilike(search_term))
    
    if question_type:
        query = query.filter(FormQuestion.question_type == question_type)
    
    # Get total count
    total_count = query.count()
    
    # Apply ordering and pagination
    questions = query.order_by(FormQuestion.section, FormQuestion.order_index).offset(offset).limit(limit).all()
    
    # Get unique sections
    sections = db.query(FormQuestion.section).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.is_active == True,
        FormQuestion.section.isnot(None)
    ).distinct().all()
    section_list = [s[0] for s in sections if s[0]]
    
    return QuestionListResponse(
        questions=questions,
        total_count=total_count,
        document_type=doc_type,
        sections=section_list
    )


@router.post("/{doc_type}/questions", response_model=QuestionResponse)
async def add_question(
    doc_type: str,
    question_data: QuestionCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Add question to schema (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Check if question ID already exists
    existing_question = db.query(FormQuestion).filter(
        FormQuestion.form_schema_id == schema.id,
        FormQuestion.question_id == question_data.question_id
    ).first()
    
    if existing_question:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Question with ID '{question_data.question_id}' already exists"
        )
    
    # Create question
    question = FormQuestion(
        form_schema_id=schema.id,
        question_id=question_data.question_id,
        question_text=question_data.question_text,
        question_type=question_data.question_type,
        description=question_data.description,
        placeholder=question_data.placeholder,
        options=[opt.dict() for opt in question_data.options],
        validation_rules=[rule.dict() for rule in question_data.validation_rules],
        question_metadata=question_data.metadata,
        is_required=question_data.is_required,
        order_index=question_data.order_index,
        section=question_data.section,
        created_by=current_user.id,
        tenant_id=current_tenant.id
    )
    
    db.add(question)
    db.flush()  # Get the question ID
    
    # Create conditional logic rules
    for logic in question_data.conditional_logic:
        rule = ConditionalRule(
            form_schema_id=schema.id,
            question_id=question.id,
            condition_question_id=logic.condition_question_id,
            operator=logic.operator,
            condition_value=logic.value,
            action=logic.action,
            target_question_ids=logic.target_question_ids,
            created_by=current_user.id,
            tenant_id=current_tenant.id
        )
        db.add(rule)
    
    db.commit()
    db.refresh(question)
    
    return question


@router.put("/{doc_type}/questions/{q_id}", response_model=QuestionResponse)
async def update_question(
    doc_type: str,
    q_id: uuid.UUID,
    question_data: QuestionUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    # Update fields
    update_data = question_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "options" and value is not None:
            setattr(question, field, [opt.dict() for opt in value])
        elif field == "validation_rules" and value is not None:
            setattr(question, field, [rule.dict() for rule in value])
        elif field == "conditional_logic" and value is not None:
            # Update conditional logic rules
            # First, delete existing rules
            db.query(ConditionalRule).filter(
                ConditionalRule.question_id == question.id
            ).delete()
            
            # Create new rules
            for logic in value:
                rule = ConditionalRule(
                    form_schema_id=schema.id,
                    question_id=question.id,
                    condition_question_id=logic.condition_question_id,
                    operator=logic.operator,
                    condition_value=logic.value,
                    action=logic.action,
                    target_question_ids=logic.target_question_ids,
                    created_by=current_user.id,
                    tenant_id=current_tenant.id
                )
                db.add(rule)
        else:
            setattr(question, field, value)
    
    question.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(question)
    
    return question


@router.delete("/{doc_type}/questions/{q_id}", response_model=MessageResponse)
async def delete_question(
    doc_type: str,
    q_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Delete question (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get question
    question = db.query(FormQuestion).filter(
        FormQuestion.id == q_id,
        FormQuestion.form_schema_id == schema.id
    ).first()
    
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    
    # Soft delete by marking as inactive
    question.is_active = False
    question.updated_at = datetime.now(timezone.utc)
    
    # Also deactivate related conditional rules
    db.query(ConditionalRule).filter(
        ConditionalRule.question_id == question.id
    ).update({"is_active": False})
    
    db.commit()
    
    return MessageResponse(
        message="Question deleted successfully",
        details={"question_id": str(q_id)}
    )


@router.get("/{doc_type}/conditional", response_model=ConditionalLogicResponse)
async def get_conditional_logic(
    doc_type: str,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get conditional question logic
    """
    # Get schema
    schema = get_form_schema_or_404(doc_type, current_tenant.id, db)
    
    # Get all conditional rules for this schema
    rules = db.query(ConditionalRule).filter(
        ConditionalRule.form_schema_id == schema.id,
        ConditionalRule.is_active == True
    ).all()
    
    # Convert to response format
    rule_responses = []
    for rule in rules:
        # Get the question to get the question_id
        question = db.query(FormQuestion).filter(FormQuestion.id == rule.question_id).first()
        if question:
            rule_response = ConditionalRuleResponse(
                id=rule.id,
                question_id=question.question_id,
                condition_question_id=rule.condition_question_id,
                operator=rule.operator,
                value=rule.condition_value,
                action=rule.action,
                target_question_ids=rule.target_question_ids,
                is_active=rule.is_active
            )
            rule_responses.append(rule_response)
    
    return ConditionalLogicResponse(
        document_type=doc_type,
        rules=rule_responses,
        total_rules=len(rule_responses)
    )
