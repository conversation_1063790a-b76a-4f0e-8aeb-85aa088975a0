"""
Learning & AI Enhancement API endpoints.
Handles learning profiles, feedback, patterns, and AI enhancement features.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_
from typing import List, Dict, Any
import uuid
from datetime import datetime, timezone, timedelta

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.document_type import DocumentType
from app.models.learning import LearningProfile, AIFeedback, LearnedPattern, LearningSession
from app.schemas.learning import (
    LearningProfileResponse,
    LearningProfileUpdate,
    AIFeedbackCreate,
    AIFeedbackResponse,
    LearnedPatternCreate,
    LearnedPatternResponse,
    LearningSuggestionsResponse,
    LearningSettingsUpdate,
    LearningSettingsResponse,
    ResetLearningRequest,
    ResetLearningResponse,
    MessageResponse
)
from app.services.anthropic_service import anthropic_service

router = APIRouter()


@router.get("/profile/{doc_type}", response_model=LearningProfileResponse)
async def get_learning_profile(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning profile for document type
    """
    # Verify document type exists and user has access
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile:
        # Create default profile
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)
        db.commit()
        db.refresh(profile)
    
    return profile


@router.put("/profile/{doc_type}", response_model=LearningProfileResponse)
async def update_learning_profile(
    doc_type: uuid.UUID,
    profile_update: LearningProfileUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learning profile for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile:
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)
    
    # Update profile
    update_data = profile_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "settings" and value:
            # Merge settings
            current_settings = profile.settings or {}
            current_settings.update(value)
            profile.settings = current_settings
        else:
            setattr(profile, field, value)
    
    profile.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(profile)
    
    return profile


@router.post("/feedback", response_model=AIFeedbackResponse)
async def submit_feedback(
    feedback_data: AIFeedbackCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Submit feedback on AI output
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == feedback_data.document_type_id,
        DocumentType.is_active == True
    ).first()
    
    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )
    
    # If document_id provided, verify it exists and user has access
    if feedback_data.document_id:
        document = db.query(Document).filter(
            Document.id == feedback_data.document_id,
            Document.tenant_id == current_tenant.id
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
    
    # Create feedback record
    feedback = AIFeedback(
        document_id=feedback_data.document_id,
        document_type_id=feedback_data.document_type_id,
        user_id=current_user.id,
        tenant_id=current_tenant.id,
        feedback_type=feedback_data.feedback_type,
        rating=feedback_data.rating,
        sentiment=feedback_data.sentiment,
        ai_output_section=feedback_data.ai_output_section,
        original_prompt=feedback_data.original_prompt,
        ai_generated_content=feedback_data.ai_generated_content,
        feedback_text=feedback_data.feedback_text,
        suggested_improvement=feedback_data.suggested_improvement,
        tags=feedback_data.tags
    )
    
    db.add(feedback)
    db.commit()
    db.refresh(feedback)
    
    # Update learning profile statistics
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == feedback_data.document_type_id,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if profile:
        profile.total_feedback_received += 1
        if feedback_data.sentiment == "positive":
            profile.positive_feedback_count += 1
        elif feedback_data.sentiment == "negative":
            profile.negative_feedback_count += 1
        
        profile.updated_at = datetime.now(timezone.utc)
        db.commit()
    
    # TODO: Trigger async learning process to analyze feedback
    # This would be implemented as a background task
    
    return feedback


@router.get("/suggestions/{doc_id}", response_model=LearningSuggestionsResponse)
async def get_learning_suggestions(
    doc_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning-based suggestions for document
    """
    # Verify document exists and user has access
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.tenant_id == current_tenant.id
    ).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found"
        )
    
    # Get learning profile for document type
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == document.document_type_id,
        LearningProfile.tenant_id == current_tenant.id
    ).first()
    
    if not profile or not profile.learning_enabled:
        return LearningSuggestionsResponse(
            suggestions=[],
            total_suggestions=0,
            document_id=doc_id,
            document_type_id=document.document_type_id,
            generated_at=datetime.now(timezone.utc)
        )
    
    # Get applicable patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == document.document_type_id,
        LearnedPattern.tenant_id == current_tenant.id,
        LearnedPattern.is_active == True,
        LearnedPattern.confidence_score >= profile.confidence_threshold
    ).all()
    
    try:
        # Generate AI-powered suggestions based on learned patterns
        suggestions = await anthropic_service.generate_learning_suggestions(
            document.content,
            document.form_data,
            patterns,
            profile.user_preferences
        )
        
        return LearningSuggestionsResponse(
            suggestions=suggestions,
            total_suggestions=len(suggestions),
            document_id=doc_id,
            document_type_id=document.document_type_id,
            generated_at=datetime.now(timezone.utc)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate learning suggestions: {str(e)}"
        )


@router.post("/pattern/{doc_type}", response_model=LearnedPatternResponse)
async def record_pattern(
    doc_type: uuid.UUID,
    pattern_data: LearnedPatternCreate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Record new pattern for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Check if pattern with same name already exists
    existing_pattern = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id,
        LearnedPattern.pattern_name == pattern_data.pattern_name
    ).first()

    if existing_pattern:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Pattern with this name already exists"
        )

    # Create new pattern
    pattern = LearnedPattern(
        document_type_id=doc_type,
        tenant_id=current_tenant.id,
        pattern_type=pattern_data.pattern_type,
        pattern_name=pattern_data.pattern_name,
        pattern_description=pattern_data.pattern_description,
        pattern_data=pattern_data.pattern_data,
        trigger_conditions=pattern_data.trigger_conditions,
        auto_apply=pattern_data.auto_apply,
        confidence_score=0.5  # Initial confidence
    )

    db.add(pattern)
    db.commit()
    db.refresh(pattern)

    # Update learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if profile:
        profile.last_learning_update = datetime.now(timezone.utc)
        db.commit()

    return pattern


@router.get("/patterns/{doc_type}", response_model=List[LearnedPatternResponse])
async def get_patterns(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learned patterns for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get patterns
    patterns = db.query(LearnedPattern).filter(
        LearnedPattern.document_type_id == doc_type,
        LearnedPattern.tenant_id == current_tenant.id
    ).order_by(desc(LearnedPattern.confidence_score)).all()

    return patterns


@router.put("/settings/{doc_type}", response_model=LearningSettingsResponse)
async def update_learning_settings(
    doc_type: uuid.UUID,
    settings_update: LearningSettingsUpdate,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Update learning settings for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get or create learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if not profile:
        profile = LearningProfile(
            document_type_id=doc_type,
            tenant_id=current_tenant.id
        )
        db.add(profile)

    # Update settings
    current_settings = profile.settings or {}
    update_data = settings_update.dict(exclude_unset=True)
    current_settings.update(update_data)
    profile.settings = current_settings
    profile.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(profile)

    return LearningSettingsResponse(**profile.settings)


@router.get("/settings/{doc_type}", response_model=LearningSettingsResponse)
async def get_learning_settings(
    doc_type: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Get learning settings for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Get learning profile
    profile = db.query(LearningProfile).filter(
        LearningProfile.document_type_id == doc_type,
        LearningProfile.tenant_id == current_tenant.id
    ).first()

    if not profile:
        # Return default settings
        return LearningSettingsResponse(
            pattern_detection_sensitivity="medium",
            feedback_weight=1.0,
            pattern_expiry_days=90,
            max_patterns_stored=100
        )

    return LearningSettingsResponse(**profile.settings)


@router.post("/reset/{doc_type}", response_model=ResetLearningResponse)
async def reset_learning_data(
    doc_type: uuid.UUID,
    reset_request: ResetLearningRequest,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """
    Reset learning data for document type
    """
    # Verify document type exists
    document_type = db.query(DocumentType).filter(
        DocumentType.id == doc_type,
        DocumentType.is_active == True
    ).first()

    if not document_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document type not found"
        )

    # Verify confirmation
    if reset_request.confirmation != "RESET":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid confirmation. Must be 'RESET'"
        )

    patterns_removed = 0
    feedback_removed = 0
    profile_reset = False

    # Reset patterns
    if reset_request.reset_patterns:
        patterns_query = db.query(LearnedPattern).filter(
            LearnedPattern.document_type_id == doc_type,
            LearnedPattern.tenant_id == current_tenant.id
        )
        patterns_removed = patterns_query.count()
        patterns_query.delete()

    # Reset feedback
    if reset_request.reset_feedback:
        feedback_query = db.query(AIFeedback).filter(
            AIFeedback.document_type_id == doc_type,
            AIFeedback.tenant_id == current_tenant.id
        )
        feedback_removed = feedback_query.count()
        feedback_query.delete()

    # Reset profile
    if reset_request.reset_profile:
        profile = db.query(LearningProfile).filter(
            LearningProfile.document_type_id == doc_type,
            LearningProfile.tenant_id == current_tenant.id
        ).first()

        if profile:
            # Reset statistics and data
            profile.total_documents_processed = 0
            profile.total_feedback_received = 0
            profile.positive_feedback_count = 0
            profile.negative_feedback_count = 0
            profile.learned_patterns = []
            profile.user_preferences = {}
            profile.performance_metrics = {}
            profile.last_learning_update = None
            profile.updated_at = datetime.now(timezone.utc)
            profile_reset = True

    db.commit()

    return ResetLearningResponse(
        message="Learning data reset successfully",
        patterns_removed=patterns_removed,
        feedback_removed=feedback_removed,
        profile_reset=profile_reset,
        reset_at=datetime.now(timezone.utc)
    )
