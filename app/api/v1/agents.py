"""
AI Agent Management API endpoints.
Handles CRUD operations for AI agents with proper authentication and authorization.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional
import uuid

from app.core.database import get_db
from app.api.deps import get_current_user, get_current_tenant
from app.models.user import User
from app.models.tenant import Tenant
from app.models.agent import AIAgent, AgentStatus, AgentType
from app.schemas.agent import (
    AgentCreate,
    AgentUpdate,
    AgentResponse,
    AgentListItem,
    AgentListResponse,
    MessageResponse
)

router = APIRouter()


def check_admin_permissions(user: User) -> bool:
    """Check if user has admin permissions for agent management"""
    # In a real implementation, check user roles/permissions
    return True


def get_agents_query(db: Session, doc_type: str, tenant_id: Optional[uuid.UUID] = None):
    """Get base query for agents filtered by doc_type and tenant"""
    query = db.query(AIAgent).filter(AIAgent.doc_type == doc_type)
    
    if tenant_id:
        # Include system agents and tenant-specific agents
        query = query.filter(
            or_(
                AIAgent.tenant_id == tenant_id,
                AIAgent.tenant_id.is_(None)  # System agents
            )
        )
    else:
        # Only system agents for non-tenant requests
        query = query.filter(AIAgent.tenant_id.is_(None))
    
    return query


@router.get("/{doc_type}", response_model=AgentListResponse)
async def get_agents_for_doc_type(
    doc_type: str,
    status: Optional[AgentStatus] = Query(None, description="Filter by agent status"),
    agent_type: Optional[AgentType] = Query(None, description="Filter by agent type"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get all agents for a specific document type
    """
    query = get_agents_query(db, doc_type, current_tenant.id)
    
    # Apply filters
    if status:
        query = query.filter(AIAgent.status == status)
    if agent_type:
        query = query.filter(AIAgent.agent_type == agent_type)
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    agents = query.order_by(AIAgent.created_at.desc()).offset(offset).limit(limit).all()
    
    return AgentListResponse(
        agents=[AgentListItem.from_orm(agent) for agent in agents],
        total=total
    )


@router.get("/{doc_type}/{agent_id}", response_model=AgentResponse)
async def get_agent_details(
    doc_type: str,
    agent_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Get specific agent details
    """
    query = get_agents_query(db, doc_type, current_tenant.id)
    agent = query.filter(AIAgent.id == agent_id).first()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found"
        )
    
    return AgentResponse.from_orm(agent)


@router.post("/{doc_type}", response_model=AgentResponse)
async def create_agent(
    doc_type: str,
    agent_data: AgentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Create a new custom agent (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Ensure doc_type matches
    if agent_data.doc_type != doc_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Agent doc_type must match URL parameter"
        )
    
    # Check for duplicate names within tenant/doc_type
    existing_agent = db.query(AIAgent).filter(
        and_(
            AIAgent.name == agent_data.name,
            AIAgent.doc_type == doc_type,
            AIAgent.tenant_id == current_tenant.id
        )
    ).first()
    
    if existing_agent:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Agent with this name already exists for this document type"
        )
    
    # Set default capabilities if not provided
    capabilities = agent_data.capabilities or {
        "document_generation": True,
        "refinement": True,
        "follow_up_questions": True,
        "suggestions": True
    }
    
    # Create agent
    agent = AIAgent(
        name=agent_data.name,
        description=agent_data.description,
        doc_type=doc_type,
        agent_type=agent_data.agent_type,
        model_name=agent_data.model_name,
        system_prompt=agent_data.system_prompt,
        temperature=agent_data.temperature,
        max_tokens=agent_data.max_tokens,
        capabilities=capabilities,
        parameters=agent_data.parameters or {},
        tenant_id=current_tenant.id,
        created_by=current_user.id
    )
    
    db.add(agent)
    db.commit()
    db.refresh(agent)
    
    return AgentResponse.from_orm(agent)


@router.put("/{doc_type}/{agent_id}", response_model=AgentResponse)
async def update_agent(
    doc_type: str,
    agent_id: uuid.UUID,
    agent_data: AgentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Update agent configuration (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get agent (only tenant-owned agents can be updated)
    agent = db.query(AIAgent).filter(
        and_(
            AIAgent.id == agent_id,
            AIAgent.doc_type == doc_type,
            AIAgent.tenant_id == current_tenant.id
        )
    ).first()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found or not editable"
        )
    
    # Check for name conflicts if name is being updated
    if agent_data.name and agent_data.name != agent.name:
        existing_agent = db.query(AIAgent).filter(
            and_(
                AIAgent.name == agent_data.name,
                AIAgent.doc_type == doc_type,
                AIAgent.tenant_id == current_tenant.id,
                AIAgent.id != agent_id
            )
        ).first()
        
        if existing_agent:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent with this name already exists"
            )
    
    # Update fields
    update_data = agent_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(agent, field, value)
    
    db.commit()
    db.refresh(agent)
    
    return AgentResponse.from_orm(agent)


@router.delete("/{doc_type}/{agent_id}", response_model=MessageResponse)
async def delete_agent(
    doc_type: str,
    agent_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    Delete an agent (admin only)
    """
    if not check_admin_permissions(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin permissions required"
        )
    
    # Get agent (only tenant-owned agents can be deleted)
    agent = db.query(AIAgent).filter(
        and_(
            AIAgent.id == agent_id,
            AIAgent.doc_type == doc_type,
            AIAgent.tenant_id == current_tenant.id
        )
    ).first()
    
    if not agent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Agent not found or not deletable"
        )
    
    # Prevent deletion of system default agents
    if agent.is_system_default:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system default agents"
        )
    
    db.delete(agent)
    db.commit()
    
    return MessageResponse(message="Agent deleted successfully")
