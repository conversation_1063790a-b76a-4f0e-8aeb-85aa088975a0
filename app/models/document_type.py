from sqlalchemy import Column, String, DateTime, Boolean, Text, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid
from app.core.database import Base

class DocumentType(Base):
    __tablename__ = "document_types"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    slug = Column(String, unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String, nullable=False)  # prd, brd, technical_spec, user_story, etc.
    industry = Column(String, nullable=True)  # tech, healthcare, finance, etc.
    
    # Form configuration
    form_schema = Column(JSON, nullable=False)  # Questions and validation rules
    
    # Template configuration
    template_structure = Column(JSON, nullable=False)  # Document sections and format
    
    # AI configuration
    ai_agents = Column(JSON, default={
        "primary_agent": "claude-3-5-sonnet-20241022",
        "refinement_agents": [],
        "specialized_agents": {}
    })
    
    # Refinement options
    refinement_options = Column(JSON, default={
        "tone_options": ["professional", "casual", "technical"],
        "length_options": ["brief", "standard", "detailed"],
        "focus_areas": [],
        "custom_refinements": []
    })
    
    # Metadata
    is_system_default = Column(Boolean, default=False)  # Built-in vs custom
    is_active = Column(Boolean, default=True)
    version = Column(String, default="1.0")
    usage_count = Column(Integer, default=0)
    
    # Tenant association (null for system defaults)
    tenant_id = Column(UUID(as_uuid=True), nullable=True)
    created_by = Column(UUID(as_uuid=True), nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
