"""
Form schema and question management database models.
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, Boolean, JSON, ForeignKey, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class FormSchema(Base):
    """Form schemas for document types"""
    __tablename__ = "form_schemas"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Schema details
    document_type = Column(String, nullable=False, unique=True)  # One schema per document type
    schema_version = Column(String, default="1.0")
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Schema configuration
    sections = Column(JSON, default=[])  # List of section names
    global_settings = Column(JSON, default={})  # Global form settings
    styling = Column(JSON, default={})  # Form styling configuration
    schema_metadata = Column(JSON, default={})  # Additional schema metadata
    
    # Status
    is_active = Column(Boolean, default=True)
    is_published = Column(Boolean, default=False)
    
    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    questions = relationship("FormQuestion", backref="form_schema", cascade="all, delete-orphan")
    conditional_rules = relationship("ConditionalRule", backref="form_schema", cascade="all, delete-orphan")


class FormQuestion(Base):
    """Form questions"""
    __tablename__ = "form_questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    form_schema_id = Column(UUID(as_uuid=True), ForeignKey("form_schemas.id"), nullable=False)
    
    # Question details
    question_id = Column(String, nullable=False)  # Unique within schema
    question_text = Column(String, nullable=False)
    question_type = Column(String, nullable=False)  # text, select, etc.
    description = Column(Text, nullable=True)
    placeholder = Column(String, nullable=True)
    
    # Question configuration
    options = Column(JSON, default=[])  # For select/radio/checkbox questions
    validation_rules = Column(JSON, default=[])  # Validation rules
    question_metadata = Column(JSON, default={})  # Additional question metadata
    
    # Display and behavior
    is_required = Column(Boolean, default=False)
    order_index = Column(Integer, default=0)
    section = Column(String, nullable=True)  # Section/group name
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    conditional_rules = relationship("ConditionalRule", backref="question", cascade="all, delete-orphan")


class ConditionalRule(Base):
    """Conditional logic rules for questions"""
    __tablename__ = "conditional_rules"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    form_schema_id = Column(UUID(as_uuid=True), ForeignKey("form_schemas.id"), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey("form_questions.id"), nullable=False)
    
    # Condition details
    condition_question_id = Column(String, nullable=False)  # Question ID that triggers condition
    operator = Column(String, nullable=False)  # equals, greater_than, etc.
    condition_value = Column(JSON, nullable=False)  # Value to compare against
    
    # Action details
    action = Column(String, nullable=False)  # show, hide, require, etc.
    target_question_ids = Column(JSON, default=[])  # Questions affected by this rule
    
    # Rule metadata
    rule_metadata = Column(JSON, default={})
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class FormValidationLog(Base):
    """Form validation logs for analytics"""
    __tablename__ = "form_validation_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    form_schema_id = Column(UUID(as_uuid=True), ForeignKey("form_schemas.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    document_id = Column(UUID(as_uuid=True), nullable=True)  # If validation was for document creation
    
    # Validation details
    form_data = Column(JSON, nullable=False)  # Submitted form data
    validation_result = Column(JSON, nullable=False)  # Validation results
    is_valid = Column(Boolean, nullable=False)
    error_count = Column(Integer, default=0)
    warning_count = Column(Integer, default=0)
    
    # Context
    user_agent = Column(String, nullable=True)
    ip_address = Column(String, nullable=True)
    session_id = Column(String, nullable=True)
    
    # Timing
    validation_time_ms = Column(Integer, nullable=True)  # Time taken for validation
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class QuestionUsageStats(Base):
    """Question usage statistics"""
    __tablename__ = "question_usage_stats"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # References
    form_question_id = Column(UUID(as_uuid=True), ForeignKey("form_questions.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Usage metrics
    total_submissions = Column(Integer, default=0)
    valid_submissions = Column(Integer, default=0)
    invalid_submissions = Column(Integer, default=0)
    skip_rate = Column(Float, default=0.0)  # Percentage of times question was skipped
    
    # Response analysis
    most_common_responses = Column(JSON, default=[])  # For select/radio questions
    average_response_length = Column(Float, nullable=True)  # For text questions
    response_time_avg_seconds = Column(Float, nullable=True)
    
    # Date tracking
    stats_date = Column(DateTime(timezone=True), nullable=False)  # Date these stats are for
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class FormSchemaVersion(Base):
    """Form schema version history"""
    __tablename__ = "form_schema_versions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    form_schema_id = Column(UUID(as_uuid=True), ForeignKey("form_schemas.id"), nullable=False)
    
    # Version details
    version_number = Column(String, nullable=False)
    version_name = Column(String, nullable=True)
    change_summary = Column(Text, nullable=True)
    
    # Snapshot data
    schema_snapshot = Column(JSON, nullable=False)  # Complete schema at this version
    questions_snapshot = Column(JSON, nullable=False)  # All questions at this version
    
    # Version metadata
    is_published = Column(Boolean, default=False)
    is_current = Column(Boolean, default=False)
    
    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)


class QuestionTemplate(Base):
    """Reusable question templates"""
    __tablename__ = "question_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String, nullable=True)
    
    # Question template data
    question_type = Column(String, nullable=False)
    template_data = Column(JSON, nullable=False)  # Question template structure
    default_validation_rules = Column(JSON, default=[])
    
    # Applicability
    document_types = Column(JSON, default=[])  # Applicable document types
    tags = Column(JSON, default=[])
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    is_public = Column(Boolean, default=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Lifecycle
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
