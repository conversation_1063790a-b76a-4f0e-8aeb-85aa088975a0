from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from app.core.database import Base

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    
    # Document type reference
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    
    # Form data used to generate the document
    form_data = Column(JSON, nullable=False)
    
    # Document metadata
    status = Column(String, default="draft")  # draft, review, approved, published, archived
    version = Column(String, default="1.0")
    version_number = Column(Integer, default=1)
    
    # Ownership and tenant
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Collaboration
    shared_with = Column(JSON, default=[])  # List of user IDs with access
    permissions = Column(JSON, default={
        "can_view": [],
        "can_edit": [],
        "can_comment": []
    })
    
    # Analytics
    view_count = Column(Integer, default=0)
    last_viewed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)

class DocumentVersion(Base):
    __tablename__ = "document_versions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    
    # Version details
    version = Column(String, nullable=False)
    version_number = Column(Integer, nullable=False)
    
    # Content snapshot
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    form_data = Column(JSON, nullable=False)
    status = Column(String, nullable=False)
    
    # Change metadata
    change_summary = Column(String, nullable=True)
    created_by = Column(UUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", backref="versions")

class DocumentGenerationSession(Base):
    __tablename__ = "document_generation_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Session data
    session_type = Column(String, nullable=False)  # initial, followup, regeneration
    status = Column(String, default="pending")  # pending, processing, completed, failed
    progress = Column(Integer, default=0)

    # Generation context
    original_form_data = Column(JSON, nullable=False)
    followup_questions = Column(JSON, default=[])
    followup_answers = Column(JSON, default={})
    generation_instructions = Column(Text, nullable=True)

    # Results
    generated_content = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    document = relationship("Document", backref="generation_sessions")

class DocumentRefinementJob(Base):
    __tablename__ = "document_refinement_jobs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Job details
    job_type = Column(String, nullable=False)  # section, component, custom, suggestions
    status = Column(String, default="pending")  # pending, processing, completed, failed, cancelled
    progress = Column(Integer, default=0)

    # Refinement context
    target = Column(String, nullable=False)  # section name, component id, or "document"
    refinement_type = Column(String, nullable=False)
    instructions = Column(Text, nullable=True)
    parameters = Column(JSON, default={})

    # Original and refined content
    original_content = Column(Text, nullable=True)
    refined_content = Column(Text, nullable=True)

    # Job metadata
    estimated_completion = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    document = relationship("Document", backref="refinement_jobs")
