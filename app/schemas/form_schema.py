"""
Form schema and question management related Pydantic schemas.
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
from enum import Enum
import uuid


class QuestionType(str, Enum):
    """Question types"""
    TEXT = "text"
    TEXTAREA = "textarea"
    NUMBER = "number"
    EMAIL = "email"
    URL = "url"
    DATE = "date"
    DATETIME = "datetime"
    SELECT = "select"
    MULTISELECT = "multiselect"
    RADIO = "radio"
    CHECKBOX = "checkbox"
    BOOLEAN = "boolean"
    FILE = "file"
    RANGE = "range"
    RATING = "rating"


class ValidationRule(str, Enum):
    """Validation rule types"""
    REQUIRED = "required"
    MIN_LENGTH = "min_length"
    MAX_LENGTH = "max_length"
    MIN_VALUE = "min_value"
    MAX_VALUE = "max_value"
    PATTERN = "pattern"
    EMAIL_FORMAT = "email_format"
    URL_FORMAT = "url_format"
    CUSTOM = "custom"


class ConditionalOperator(str, Enum):
    """Conditional logic operators"""
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    IN = "in"
    NOT_IN = "not_in"
    IS_EMPTY = "is_empty"
    IS_NOT_EMPTY = "is_not_empty"


# Base schemas
class QuestionOption(BaseModel):
    """Question option for select/radio/checkbox questions"""
    value: str = Field(..., description="Option value")
    label: str = Field(..., description="Option display label")
    description: Optional[str] = Field(None, description="Option description")
    is_default: bool = Field(False, description="Whether this is the default option")


class ValidationRuleSchema(BaseModel):
    """Validation rule schema"""
    rule_type: ValidationRule = Field(..., description="Type of validation rule")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="Rule value")
    message: Optional[str] = Field(None, description="Custom error message")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Additional rule parameters")


class ConditionalLogic(BaseModel):
    """Conditional logic for questions"""
    condition_question_id: str = Field(..., description="ID of question that triggers condition")
    operator: ConditionalOperator = Field(..., description="Conditional operator")
    value: Union[str, int, float, bool, List[str]] = Field(..., description="Value to compare against")
    action: str = Field(..., description="Action to take (show, hide, require, etc.)")
    target_question_ids: List[str] = Field(default_factory=list, description="Questions affected by this condition")


# Question schemas
class QuestionBase(BaseModel):
    """Base question schema"""
    question_id: str = Field(..., description="Unique question identifier")
    question_text: str = Field(..., min_length=1, max_length=500, description="Question text")
    question_type: QuestionType = Field(..., description="Type of question")
    description: Optional[str] = Field(None, max_length=1000, description="Question description/help text")
    placeholder: Optional[str] = Field(None, description="Placeholder text")


class QuestionCreate(QuestionBase):
    """Create question request"""
    options: List[QuestionOption] = Field(default_factory=list, description="Options for select/radio/checkbox")
    validation_rules: List[ValidationRuleSchema] = Field(default_factory=list, description="Validation rules")
    conditional_logic: List[ConditionalLogic] = Field(default_factory=list, description="Conditional logic rules")
    is_required: bool = Field(False, description="Whether question is required")
    order_index: int = Field(0, description="Display order")
    section: Optional[str] = Field(None, description="Section/group this question belongs to")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional question metadata")


class QuestionUpdate(BaseModel):
    """Update question request"""
    question_text: Optional[str] = Field(None, min_length=1, max_length=500)
    question_type: Optional[QuestionType] = Field(None)
    description: Optional[str] = Field(None, max_length=1000)
    placeholder: Optional[str] = Field(None)
    options: Optional[List[QuestionOption]] = Field(None)
    validation_rules: Optional[List[ValidationRuleSchema]] = Field(None)
    conditional_logic: Optional[List[ConditionalLogic]] = Field(None)
    is_required: Optional[bool] = Field(None)
    order_index: Optional[int] = Field(None)
    section: Optional[str] = Field(None)
    metadata: Optional[Dict[str, Any]] = Field(None)
    is_active: Optional[bool] = Field(None, description="Whether question is active")


class QuestionResponse(QuestionBase):
    """Question response"""
    id: uuid.UUID
    document_type: str
    options: List[QuestionOption]
    validation_rules: List[ValidationRuleSchema]
    conditional_logic: List[ConditionalLogic]
    is_required: bool
    order_index: int
    section: Optional[str]
    question_metadata: Dict[str, Any]
    is_active: bool
    
    # Ownership
    created_by: uuid.UUID
    tenant_id: uuid.UUID
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Form schema schemas
class FormSchemaBase(BaseModel):
    """Base form schema"""
    document_type: str = Field(..., description="Document type this schema is for")
    schema_version: str = Field("1.0", description="Schema version")
    title: str = Field(..., min_length=1, max_length=200, description="Form title")
    description: Optional[str] = Field(None, max_length=1000, description="Form description")


class FormSchemaCreate(FormSchemaBase):
    """Create form schema request"""
    sections: List[str] = Field(default_factory=list, description="Form sections")
    global_settings: Dict[str, Any] = Field(default_factory=dict, description="Global form settings")
    styling: Dict[str, Any] = Field(default_factory=dict, description="Form styling configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional schema metadata")


class FormSchemaUpdate(BaseModel):
    """Update form schema request"""
    schema_version: Optional[str] = Field(None)
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    sections: Optional[List[str]] = Field(None)
    global_settings: Optional[Dict[str, Any]] = Field(None)
    styling: Optional[Dict[str, Any]] = Field(None)
    metadata: Optional[Dict[str, Any]] = Field(None)
    is_active: Optional[bool] = Field(None, description="Whether schema is active")


class FormSchemaResponse(FormSchemaBase):
    """Form schema response"""
    id: uuid.UUID
    sections: List[str]
    global_settings: Dict[str, Any]
    styling: Dict[str, Any]
    schema_metadata: Dict[str, Any]
    is_active: bool
    question_count: int
    
    # Ownership
    created_by: uuid.UUID
    tenant_id: uuid.UUID
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class FormSchemaWithQuestions(FormSchemaResponse):
    """Form schema with questions included"""
    questions: List[QuestionResponse]


# Validation schemas
class FormValidationRequest(BaseModel):
    """Form validation request"""
    form_data: Dict[str, Any] = Field(..., description="Form data to validate")
    validate_conditionals: bool = Field(True, description="Whether to validate conditional logic")


class ValidationError(BaseModel):
    """Validation error"""
    question_id: str = Field(..., description="Question ID with error")
    error_type: str = Field(..., description="Type of validation error")
    message: str = Field(..., description="Error message")
    value: Optional[Any] = Field(None, description="Invalid value")


class FormValidationResponse(BaseModel):
    """Form validation response"""
    is_valid: bool = Field(..., description="Whether form data is valid")
    errors: List[ValidationError] = Field(default_factory=list, description="Validation errors")
    warnings: List[ValidationError] = Field(default_factory=list, description="Validation warnings")
    conditional_questions: List[str] = Field(default_factory=list, description="Questions shown based on conditionals")
    hidden_questions: List[str] = Field(default_factory=list, description="Questions hidden based on conditionals")


# Preview schemas
class FormPreviewQuestion(BaseModel):
    """Form preview question"""
    question_id: str
    question_text: str
    question_type: QuestionType
    description: Optional[str]
    placeholder: Optional[str]
    options: List[QuestionOption]
    is_required: bool
    section: Optional[str]
    order_index: int


class FormPreviewSection(BaseModel):
    """Form preview section"""
    section_name: str
    questions: List[FormPreviewQuestion]
    order_index: int


class FormPreviewResponse(BaseModel):
    """Form preview response"""
    schema_id: uuid.UUID
    document_type: str
    title: str
    description: Optional[str]
    sections: List[FormPreviewSection]
    total_questions: int
    required_questions: int
    estimated_time_minutes: int


# Conditional logic schemas
class ConditionalRuleResponse(BaseModel):
    """Conditional rule response"""
    id: uuid.UUID
    question_id: str
    condition_question_id: str
    operator: ConditionalOperator
    value: Union[str, int, float, bool, List[str]]
    action: str
    target_question_ids: List[str]
    is_active: bool
    
    class Config:
        from_attributes = True


class ConditionalLogicResponse(BaseModel):
    """Conditional logic response"""
    document_type: str
    rules: List[ConditionalRuleResponse]
    total_rules: int


# List responses
class QuestionListResponse(BaseModel):
    """Question list response"""
    questions: List[QuestionResponse]
    total_count: int
    document_type: str
    sections: List[str]


class FormSchemaListResponse(BaseModel):
    """Form schema list response"""
    schemas: List[FormSchemaResponse]
    total_count: int


# Generic response schemas
class MessageResponse(BaseModel):
    """Generic message response"""
    message: str
    details: Optional[Dict[str, Any]] = None
