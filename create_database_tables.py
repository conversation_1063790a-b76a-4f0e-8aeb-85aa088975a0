#!/usr/bin/env python3
"""
Create database tables using SQLAlchemy models.
This script will create all the necessary tables for the auth system.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def create_tables():
    """Create all database tables."""
    try:
        # Import after adding to path
        from app.core.database import Base, engine
        from app.models.user import User
        from app.models.tenant import Tenant
        
        print("🔧 Creating database tables...")
        print("=" * 50)
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        print("✅ Database tables created successfully!")
        
        # Create a session to insert default data
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Check if default tenant exists
            from sqlalchemy import text
            result = db.execute(text("SELECT COUNT(*) FROM tenants WHERE id = '00000000-0000-0000-0000-000000000000'"))
            count = result.scalar()
            
            if count == 0:
                # Insert default tenant
                db.execute(text("""
                    INSERT INTO tenants (id, name, slug, is_active) 
                    VALUES ('00000000-0000-0000-0000-000000000000', 'Default Tenant', 'default', true)
                """))
                db.commit()
                print("✅ Default tenant created!")
            else:
                print("ℹ️  Default tenant already exists")
                
        except Exception as e:
            print(f"⚠️  Could not create default tenant: {str(e)}")
            db.rollback()
        finally:
            db.close()
        
        # Verify tables were created
        print("\n📋 Verifying created tables...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                    AND table_name IN ('users', 'tenants')
                ORDER BY table_name
            """))
            
            tables = [row[0] for row in result]
            
            if tables:
                print("✅ Created tables:")
                for table in tables:
                    print(f"   - {table}")
            else:
                print("❌ No tables found!")
                return False
        
        # Check users table structure
        print("\n🔍 Checking users table structure...")
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            columns = list(result)
            if columns:
                print("✅ Users table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
            else:
                print("❌ Could not retrieve users table structure!")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        return False

def test_connection():
    """Test database connection."""
    try:
        from app.core.database import engine
        
        print("🔗 Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection test failed!")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 Database Setup Script")
    print("=" * 50)
    
    # Test connection first
    if not test_connection():
        print("\n❌ Cannot proceed without database connection.")
        print("Please check your DATABASE_URL in .env file.")
        return
    
    # Create tables
    if create_tables():
        print("\n🎉 Database setup completed successfully!")
        print("You can now test your authentication endpoints.")
    else:
        print("\n❌ Database setup failed!")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()
